import os

import torch
from diffusers import StableDiffusionPipeline

# Check if CUDA is available
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

# Option 1: Use local model path if it exists (fastest)
local_model_path = "./models/stable-diffusion-v1-5"
model_id = "runwayml/stable-diffusion-v1-5"

print("Loading Stable Diffusion model...")
if os.path.exists(local_model_path):
    print(f"Loading from local path: {local_model_path}")
    model_source = local_model_path
else:
    print(f"Loading from Hugging Face Hub: {model_id}")
    print("(Model will be cached locally for faster future loading)")
    model_source = model_id

# Load model with optimizations
if device == "cuda":
    pipe = StableDiffusionPipeline.from_pretrained(
        model_source,
        torch_dtype=torch.float16,
        use_safetensors=True,  # Faster loading
        variant="fp16",  # Use fp16 variant if available
    )
    # Enable memory efficient attention
    pipe.enable_attention_slicing()
    pipe.enable_xformers_memory_efficient_attention()
else:
    pipe = StableDiffusionPipeline.from_pretrained(
        model_source, torch_dtype=torch.float32, use_safetensors=True
    )

pipe.to(device)
print("Model loaded successfully!")

# Get prompt from user
prompt = input("Enter your image description: ")
print(f"Generating image with prompt: '{prompt}'")
print("This may take a few minutes...")

# Generate the image
image = pipe(prompt).images[0]

# Save the image
output_path = "images/generated_image.png"
image.save(output_path)
print(f"Image saved as: {output_path}")

# prompt = "A futuristic cityscape at sunset, highly detailed, cinematic lighting"
# image = pipe(prompt).images[0]

# # Save or display the generated image
# image.save("images/generated_image.png")
# # image.show() # Uncomment to display the image directly
