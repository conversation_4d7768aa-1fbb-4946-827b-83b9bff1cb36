import torch
from diffusers import StableDiffusionPipeline

# Check if CUDA is available
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

# Load a pre-trained Stable Diffusion model (e.g., v1.5)
# Use float16 for GPU, float32 for CPU for better compatibility
model_id = "runwayml/stable-diffusion-v1-5"
if device == "cuda":
    pipe = StableDiffusionPipeline.from_pretrained(model_id, torch_dtype=torch.float16)
else:
    pipe = StableDiffusionPipeline.from_pretrained(model_id, torch_dtype=torch.float32)

pipe.to(device)

# Generate an image
prompt = "A beautiful landscape with mountains and a lake at sunset"
print(f"Generating image with prompt: '{prompt}'")
print("This may take a few minutes...")

# Generate the image
image = pipe(prompt).images[0]

# Save the image
output_path = "generated_image.png"
image.save(output_path)
print(f"Image saved as: {output_path}")

prompt = "A futuristic cityscape at sunset, highly detailed, cinematic lighting"
image = pipe(prompt).images[0]

# Save or display the generated image
image.save("images/generated_image.png")
# image.show() # Uncomment to display the image directly
